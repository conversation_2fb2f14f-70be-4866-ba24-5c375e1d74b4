export interface ITicketClass {
    _id?: string;
    id?: string;
    eventId: string;
    calendarId: string;
    name: string;
    color: string;
    originalPriceVn: number;
    originalPriceUsd: number;
    finalPriceVn: number;
    finalPriceUsd: number;
    description: string;
    seatType: string;
    maxTicketPerUser: number;
    prototypeUrl: string;
    createdAt?: Date;
    updatedAt?: Date;
    isDeleted?: boolean;
}