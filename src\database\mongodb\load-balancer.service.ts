import { Injectable, Logger } from '@nestjs/common';
import { InjectConnection } from '@nestjs/mongoose';
import { Connection } from 'mongoose';

@Injectable()
export class MongoLoadBalancerService {
  private readonly logger = new Logger(MongoLoadBalancerService.name);
  private readonly readPreferenceOptions = [
    { readPreference: 'primary' },
    { readPreference: 'primaryPreferred' },
    { readPreference: 'secondary' },
    { readPreference: 'secondaryPreferred' },
    { readPreference: 'nearest' }
  ];
  private currentReadPreferenceIndex = 0;

  constructor(@InjectConnection() private readonly connection: Connection) {}

  /**
   * <PERSON><PERSON>y read preference tiếp theo theo round-robin
   */
  getNextReadPreference() {
    const option = this.readPreferenceOptions[this.currentReadPreferenceIndex];
    this.currentReadPreferenceIndex = (this.currentReadPreferenceIndex + 1) % this.readPreferenceOptions.length;
    return option;
  }

  /**
   * Th<PERSON><PERSON> hiện truy vấn với read preference động
   */
  // async executeBalancedQuery(collection: string, query: any, projection?: any) {
  //   const readPreference = this.getNextReadPreference();
  //   this.logger.debug(`Executing query with read preference: ${readPreference.readPreference}`);
    
  //   return this.connection.db.collection(collection)
  //     .find(query, { projection, ...readPreference })
  //     .toArray();
  // }
}