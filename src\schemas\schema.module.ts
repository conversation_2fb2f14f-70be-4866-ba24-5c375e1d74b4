import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

import { Order, OrderSchema } from './order/order.schema';
import { Event, EventSchema } from './event/event.schema';
import { TicketClass, TicketClassSchema } from './ticket-classes/ticket-classes.schemas';
import { TicketSummary, TicketSummarySchema } from './ticket-summary/ticket-summary.schema';
import { Promotion, PromotionSchema } from './promotions/promotions.schema';
import { Row, RowSchema } from './rows/rows.schema';
import { UserFanpass, UserFanpassSchema } from './user-fanpass/user-fanpass.schemas';
import { Zone, ZoneSchema } from './zones/zone.schema';
import { Tickets, TicketsSchema } from './tickets/tickets.schema';
import { DeadLetterQueue, DeadLetterQueueSchema } from './dead-letter-queue/dead-letter-queue.schema';
import { EventTicket, EventTicketSchema } from './event-ticket/event-ticket.schema';


const SCHEMAS = [
  { name: Order.name, schema: OrderSchema },
  { name: Event.name, schema: EventSchema },
  { name: TicketClass.name, schema: TicketClassSchema },
  { name: TicketSummary.name, schema: TicketSummarySchema },
  { name: Promotion.name, schema: PromotionSchema },
  { name: Row.name, schema: RowSchema },
  { name: UserFanpass.name, schema: UserFanpassSchema },
  { name: Zone.name, schema: ZoneSchema },
  { name: Tickets.name, schema: TicketsSchema },
  { name: DeadLetterQueue.name, schema: DeadLetterQueueSchema },
  { name: EventTicket.name, schema: EventTicketSchema },
];

@Module({
  imports: [
    MongooseModule.forFeature(SCHEMAS),
  ],
  exports: [
    MongooseModule.forFeature(SCHEMAS),
  ],
})
export class SchemaModule { }