import { Injectable, OnModuleInit } from '@nestjs/common';
import { InjectConnection } from '@nestjs/mongoose';
import { Connection } from 'mongoose';
import { CustomLogger } from '../../logger/custom.logger';

@Injectable()
export class MongodbService implements OnModuleInit {
  private readonly logger = new CustomLogger(MongodbService.name);

  constructor(
    @InjectConnection()
    private readonly connection: Connection,
  ) { }

  async onModuleInit() {
    try {
      // Kiểm tra kết nối khi khởi động
      if (this.connection.readyState === 1) {
        this.logger.log('MongoDB connected successfully');

        // Log thông tin về connection pool
        const poolInfo = this.getConnectionPoolInfo();
        this.logger.log(`MongoDB connection pool initialized: ${JSON.stringify(poolInfo)}`);
      } else {
        this.logger.warn(`MongoDB connection state: ${this.getReadyStateText(this.connection.readyState)}`);
      }

      // Thiết lập event listeners
      this.setupConnectionListeners();
    } catch (error) {
      this.logger.error('Failed to initialize MongoDB connection', error.stack);
    }
  }

  private setupConnectionListeners() {
    this.connection.on('connected', () => {
      this.logger.log('MongoDB connected');
    });

    this.connection.on('disconnected', () => {
      this.logger.warn('MongoDB disconnected');
    });

    this.connection.on('reconnected', () => {
      this.logger.log('MongoDB reconnected');
    });

    this.connection.on('error', (error) => {
      this.logger.error('MongoDB connection error', error.stack);
    });
  }

  getConnection(): Connection {
    return this.connection;
  }

  getConnectionStatus(): string {
    return this.getReadyStateText(this.connection.readyState);
  }

  async ping(): Promise<boolean> {
    try {
      await this.connection.db.admin().ping();
      return true;
    } catch (error) {
      this.logger.error('MongoDB ping failed', error.stack);
      return false;
    }
  }

  getConnectionPoolInfo() {
    // Sửa lại cách truy cập thông tin connection pool
    try {
      // Cách 1: Truy cập thông qua mongoose driver
      if (this.connection.getClient) {
        const client = this.connection.getClient();
        return {
          maxPoolSize: client.options?.maxPoolSize || 'default',
          minPoolSize: client.options?.minPoolSize || 'default',
          readyState: this.getReadyStateText(this.connection.readyState),
        };
      }

      // Cách 2: Nếu không có getClient, sử dụng cách khác
      return {
        readyState: this.getReadyStateText(this.connection.readyState),
        // Thêm các thông tin khác nếu có thể truy cập
      };
    } catch (error) {
      this.logger.warn('Could not retrieve connection pool info', error.message);
      return {
        readyState: this.getReadyStateText(this.connection.readyState),
        error: 'Could not retrieve pool information',
      };
    }
  }

  private getReadyStateText(state: number): string {
    switch (state) {
      case 0:
        return 'disconnected';
      case 1:
        return 'connected';
      case 2:
        return 'connecting';
      case 3:
        return 'disconnecting';
      default:
        return 'unknown';
    }
  }
}
