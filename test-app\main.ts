import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { ClusterService } from '../src/cluster/services/cluster.service';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  
  // Enable CORS for Postman testing
  app.enableCors();
  
  // Set global prefix
  app.setGlobalPrefix('api');
  
  await app.listen(3000);
  console.log('🚀 Application is running on: http://localhost:3000');
  console.log('📖 API Documentation: http://localhost:3000/api');
}

// Use cluster service if available
const clusterService = new ClusterService();
clusterService.startApplication(bootstrap);
