import { Module, Global } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { LoggerModule } from './logger/logger.module';
import { MongodbModule } from './database/mongodb/mongodb.module';
import { ClusterModule } from './cluster/modules/cluster.module';
import { RedisModule } from './database/redis/redis.module';
import { AuthGuard } from './auth/auth.guard';
import { APP_GUARD } from '@nestjs/core';
import { SchemaModule } from './schemas/schema.module';

@Global()
@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['/app/.env', '.env.local', '.env.dev'],
    }),
    // Đặt MongodbModule trước các module khác để đảm bảo nó được khởi tạo sớm
    MongodbModule,
    LoggerModule,
    ClusterModule,
    RedisModule,
    SchemaModule,
  ],
  exports: [
    ConfigModule,
    MongodbModule,
    LoggerModule,
    ClusterModule,
    RedisModule,
    SchemaModule,
  ],
  providers: [],
})
export class SharedModule { }
