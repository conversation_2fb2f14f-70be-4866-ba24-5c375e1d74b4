import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument } from 'mongoose';
import { BaseDocument, BaseSchema, addSoftDeleteHooks, addUpdateHooks, defaultSchemaOptions } from '../index';


export type DeadLetterQueueDocument = HydratedDocument<DeadLetterQueue> & BaseDocument;

@Schema({
    collection: 'dead_letter_queues',
    ...defaultSchemaOptions,
})
export class DeadLetterQueue extends BaseSchema {

    @Prop({
        type: String,
        default: '',
    })
    exchange: string;

    @Prop({
        type: String,

    })
    routingKey: string;

    @Prop({
        type: String,
        default: '',
    })
    queueName: string;

    @Prop(
        {
            type: String,
            default: '',
        }
    )
    message: string;
}

export const DeadLetterQueueSchema = SchemaFactory.createForClass(DeadLetterQueue);
DeadLetterQueueSchema.plugin(addUpdateHooks);
DeadLetterQueueSchema.plugin(addSoftDeleteHooks);

DeadLetterQueueSchema.index({ eventId: 1 });