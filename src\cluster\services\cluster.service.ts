import { Injectable } from '@nestjs/common';
import { CustomLogger } from '../../logger/custom.logger';

// Import node:cluster và node:os
const cluster = require('node:cluster');
const { cpus } = require('node:os');

@Injectable()
export class ClusterService {
  private logger: CustomLogger;

  constructor() {
    this.logger = new CustomLogger('ClusterService');
  }

  /**
   * Khởi động ứng dụng với chế độ cluster hoặc đơn luồng dựa trên cấu hình
   * @param bootstrapFunction Hàm khởi động ứng dụng
   */
  public startApplication(bootstrapFunction: () => Promise<void>): void {
    // Lấy giá trị ENABLE_CLUSTER từ biến môi trường, mặc định là 'false'
    const enableCluster = process.env.ENABLE_CLUSTER?.toLowerCase() === 'true';

    if (enableCluster && cluster.isPrimary) {
      this.startClusterMode();
    } else {
      this.startSingleProcessMode(bootstrapFunction, enableCluster);
    }
  }

  /**
   * Khởi động ứng dụng trong chế độ cluster
   */
  private startClusterMode(): void {
    this.logger.log(`Cluster mode enabled. Primary ${process.pid} is running`);

    // Fork workers for each CPU
    const numCPUs = cpus().length;

    // Có thể giới hạn số lượng worker thông qua biến môi trường
    const maxWorkers = parseInt(process.env.MAX_WORKERS || String(numCPUs), 10);
    const workerCount = Math.min(numCPUs, maxWorkers);

    this.logger.log(`Starting ${workerCount} workers out of ${numCPUs} available CPUs`);

    for (let i = 0; i < workerCount; i++) {
      cluster.fork();
    }

    cluster.on('exit', (worker, code, signal) => {
      this.logger.warn(`Worker ${worker.process.pid} died with code ${code} and signal ${signal}`);
      // Thay thế worker đã chết
      this.logger.log('Forking a new worker...');
      cluster.fork();
    });
  }

  /**
   * Khởi động ứng dụng trong chế độ đơn luồng hoặc như một worker
   * @param bootstrapFunction Hàm khởi động ứng dụng
   * @param isWorker Có phải là worker trong chế độ cluster không
   */
  private startSingleProcessMode(bootstrapFunction: () => Promise<void>, isWorker: boolean): void {
    bootstrapFunction().catch(err => {
      this.logger.error('Failed to start application', err);
      process.exit(1);
    });

    if (isWorker) {
      this.logger.log(`Worker ${process.pid} started`);
    } else {
      this.logger.log(`Running in single process mode. Process ${process.pid} started`);
    }
  }
}
