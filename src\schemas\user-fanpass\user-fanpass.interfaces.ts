export enum IUserFanpassStatus {
    ACTIVE = 'ACTIVE',
    INACTIVE = 'INACTIVE',
}

export interface IUserFanpassPaymentInfo{
    name: string;
    email: string;
    phoneNumber: string;
    address: string;
}

export interface IUserFanpass {
    _id?: string;
    id?: string;
    orderId: string;
    ticketId: string;
    ticketType: string;
    ticketDescription: string;
    ticketPrice: number;
    userId: string;
    status: IUserFanpassStatus;
    paymentTime?: Date;
    paymentGateway?: string;
    paymentInfo: IUserFanpassPaymentInfo;
    createdAt?: Date;
    updatedAt?: Date;
    isDeleted?: boolean;
}