import { <PERSON>p, <PERSON>hem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument } from 'mongoose';
import { BaseDocument, BaseSchema, addSoftDeleteHooks, addUpdateHooks, defaultSchemaOptions } from '../index';
import { IPromotionAppliesTo, IPromotionDiscount, IPromotionType } from './promotions.interfaces';

export type PromotionDocument = HydratedDocument<Promotion> & BaseDocument;
@Schema({
    collection: 'promotions',
    ...defaultSchemaOptions,
})
export class Promotion extends BaseSchema {

    @Prop({
        required: true,
        index: true,
    })
    eventId: string;

    @Prop({
        required: true,
        index: true,
    })
    code: string;

    @Prop({
        enum: Object.values(IPromotionAppliesTo),
        index: true,
        default: IPromotionAppliesTo.EVENT,
    })
    appliesTo: IPromotionAppliesTo;

    @Prop({
        default: true,
        type: Boolean,
    })
    active: boolean;

    @Prop({
        type: Number,
        default: 0,
    })
    maxUsage: number;

    @Prop({
        type: {
            percent: { type: Number },
            percentInUSD: { type: Number },
            maxValue: { type: Number },
            maxValueInUSD: { type: Number },
        },
        default: {},
        _id: false,
    })
    discount: IPromotionDiscount;


    @Prop({ type: [String], required: true, default: [],_id: false })
    tickets: string[];

    @Prop({
        enum: Object.values(IPromotionType),
    })
    type: IPromotionType;

    @Prop()
    minOrderAmount: number;

    @Prop()
    minOrderAmountInUSD: number;
}

export const PromotionSchema = SchemaFactory.createForClass(Promotion);
addUpdateHooks(PromotionSchema);
addSoftDeleteHooks(PromotionSchema);
PromotionSchema.index({ eventId: 1, code: 1 });