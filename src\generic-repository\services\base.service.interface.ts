import { Document, FilterQuery, UpdateQuery, QueryOptions, PipelineStage, UpdateWriteOpResult, ClientSession } from 'mongoose';

export interface IBaseService<T extends Document> {

  /**
   * Thực hiện một loạt các thao tác trong một transaction
   */
  withTransaction<TResult>(
    callback: (session: ClientSession) => Promise<TResult>,
    options?: any
  ): Promise<TResult>;

  /**
   * Sử dụng explain để phân tích truy vấn find
   */
  explain(
    filterQuery: FilterQuery<T>,
    projection?: Record<string, unknown>,
    options?: QueryOptions,
    verbosity?: 'queryPlanner' | 'executionStats' | 'allPlansExecution',
  ): Promise<any>;

  findOne(
    filterQuery: FilterQuery<T>,
    projection?: Record<string, unknown>,
    options?: QueryOptions,
  ): Promise<T | null>;

  findOneOrFail(
    filterQuery: FilterQuery<T>,
    projection?: Record<string, unknown>,
    options?: QueryOptions,
  ): Promise<T>;

  find(
    filterQuery: FilterQuery<T>,
    projection?: Record<string, unknown>,
    options?: QueryOptions,
  ): Promise<T[]>;

  create(createDto: Partial<T>): Promise<T>;

  insertMany(createDtos: Partial<T>[]): Promise<T[]>;

  update(
    filterQuery: FilterQuery<T>,
    updateQuery: UpdateQuery<T>,
    options?: QueryOptions,
  ): Promise<T | null>;

  updateOrFail(
    filterQuery: FilterQuery<T>,
    updateQuery: UpdateQuery<T>,
    options?: QueryOptions,
  ): Promise<T>;

  updateOne(
    filterQuery: FilterQuery<T>,
    updateQuery: UpdateQuery<T>,
    options?: any,
  ): Promise<UpdateWriteOpResult>;

  updateMany(
    filterQuery: FilterQuery<T>,
    updateQuery: UpdateQuery<T>,
  ): Promise<{ matchedCount: number; modifiedCount: number }>;

  delete(
    filterQuery: FilterQuery<T>,
    options?: QueryOptions,
  ): Promise<T | null>;

  deleteOrFail(
    filterQuery: FilterQuery<T>,
    options?: QueryOptions,
  ): Promise<T>;

  deleteMany(filterQuery: FilterQuery<T>): Promise<{ deletedCount: number }>;

  count(filterQuery: FilterQuery<T>): Promise<number>;

  exists(filterQuery: FilterQuery<T>): Promise<boolean>;

  aggregate(pipeline: PipelineStage[]): Promise<any[]>;

  pagination(
    filterQuery: FilterQuery<T>,
    options?: {
      page?: number;
      limit?: number;
      sort?: Record<string, 1 | -1>;
      projection?: Record<string, unknown>;
    },
  ): Promise<{
    data: T[];
    pagination: {
      total: number;
      page: number;
      limit: number;
      pages: number;
    };
  }>;

  findOneAndUpdate(
    filterQuery: FilterQuery<T>,
    updateQuery: UpdateQuery<T>,
    options?: QueryOptions,
  ): Promise<T | null>;

  findOneAndUpdateOrFail(
    filterQuery: FilterQuery<T>,
    updateQuery: UpdateQuery<T>,
    options?: QueryOptions,
  ): Promise<T>;
}