{"name": "@eventista/ticketing-common", "version": "1.0.99", "description": "common package for ticketing", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "rimraf dist && tsc", "prepublishOnly": "npm run build"}, "publishConfig": {"access": "public"}, "keywords": [], "author": "", "license": "ISC", "peerDependencies": {"@nestjs/common": "^11.0.0", "@nestjs/core": "^11.0.0", "@nestjs/mongoose": "^11.0.3", "ioredis": "^5.6.1", "jsonwebtoken": "^9.0.2", "mongoose": "^8.15.0", "rxjs": "^7.8.1"}, "devDependencies": {"@nestjs/common": "^11.1.1", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.1.1", "@nestjs/mongoose": "^11.0.3", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^20.0.0", "ioredis": "^5.6.1", "jsonwebtoken": "^9.0.2", "mongoose": "^8.15.0", "rimraf": "^5.0.0", "rxjs": "^7.8.1", "typescript": "^5.0.0"}, "dependencies": {"@nestjs/common": "^11.0.0", "@nestjs/core": "^11.0.0", "class-validator": "^0.14.2", "colorette": "^2.0.20", "uuid": "^11.1.0"}, "files": ["dist/**/*"]}